import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Play, 
  Pause, 
  Square, 
  RotateCcw, 
  Clock,
  Timer as TimerIcon,
  Settings as SettingsIcon,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import IsotopeLogo from '@/components/IsotopeLogo';
import SubjectPicker from '@/components/SubjectPicker';
import { useTimer } from '@/hooks/useTimer';

const { width } = Dimensions.get('window');

export default function TimerScreen() {
  const {
    isRunning,
    time,
    mode,
    currentSubject,
    pomodoroPhase,
    pomodoroSession,
    startTimer,
    pauseTimer,
    stopTimer,
    resetTimer,
    switchMode,
    setCurrentSubject,
    formatTime,
    getTotalTimeToday,
  } = useTimer();

  const [showSettings, setShowSettings] = useState(false);
  const pulseScale = useSharedValue(1);
  const progressRotation = useSharedValue(0);

  useEffect(() => {
    if (isRunning) {
      pulseScale.value = withRepeat(
        withTiming(1.05, { duration: 1000 }),
        -1,
        true
      );
    } else {
      pulseScale.value = withSpring(1);
    }
  }, [isRunning]);

  const timerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseScale.value }],
  }));

  const getTimerProgress = () => {
    if (mode === 'stopwatch') return 0;
    
    const totalTime = pomodoroPhase === 'work' ? 25 * 60 : 5 * 60; // Simplified for demo
    return ((totalTime - time) / totalTime) * 100;
  };

  const getPhaseText = () => {
    if (mode === 'stopwatch') return 'Stopwatch';
    return pomodoroPhase === 'work' ? `Work Session ${pomodoroSession}` : 'Break Time';
  };

  const getPhaseColor = (): [string, string] => {
    if (mode === 'stopwatch') return ['#6366F1', '#8B5CF6'];
    return pomodoroPhase === 'work' ? ['#EF4444', '#F87171'] : ['#10B981', '#34D399'];
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.headerGradient}>
        <View style={styles.header}>
          <IsotopeLogo size="medium" />
          <TouchableOpacity
            style={styles.settingsButton}
            onPress={() => setShowSettings(!showSettings)}
          >
            <SettingsIcon size={20} color="rgba(255,255,255,0.7)" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Mode Selector */}
      <View style={styles.modeSection}>
        <View style={styles.modeSelector}>
          <TouchableOpacity
            style={[
              styles.modeButton,
              mode === 'stopwatch' && styles.modeButtonActive,
            ]}
            onPress={() => switchMode('stopwatch')}
          >
            <Clock size={20} color={mode === 'stopwatch' ? '#FFFFFF' : '#6B7280'} />
            <Text
              style={[
                styles.modeText,
                mode === 'stopwatch' && styles.modeTextActive,
              ]}
            >
              Stopwatch
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.modeButton,
              mode === 'pomodoro' && styles.modeButtonActive,
            ]}
            onPress={() => switchMode('pomodoro')}
          >
            <TimerIcon size={20} color={mode === 'pomodoro' ? '#FFFFFF' : '#6B7280'} />
            <Text
              style={[
                styles.modeText,
                mode === 'pomodoro' && styles.modeTextActive,
              ]}
            >
              Pomodoro
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Subject Picker */}
      <View style={styles.subjectSection}>
        <SubjectPicker
          selectedSubject={currentSubject}
          onSelectSubject={setCurrentSubject}
        />
      </View>

      {/* Main Timer */}
      <View style={styles.timerSection}>
        <Animated.View style={[styles.timerContainer, timerAnimatedStyle]}>
          <LinearGradient
            colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
            style={styles.timerCard}
          >
            <View style={styles.timerHeader}>
              <View style={styles.phaseIndicator}>
                <LinearGradient
                  colors={getPhaseColor()}
                  style={styles.phaseGradient}
                >
                  <Text style={styles.phaseIcon}>
                    {mode === 'stopwatch' ? '⏱️' : (pomodoroPhase === 'work' ? '🍅' : '☕')}
                  </Text>
                </LinearGradient>
              </View>
              <Text style={styles.phaseText}>{getPhaseText()}</Text>
            </View>

            <View style={styles.timerDisplay}>
              <Text style={styles.timerText}>{formatTime(time)}</Text>

              {mode === 'pomodoro' && (
                <View style={styles.progressContainer}>
                  <View style={styles.progressTrack}>
                    <LinearGradient
                      colors={pomodoroPhase === 'work' ? ['#EF4444', '#F87171'] : ['#10B981', '#34D399']}
                      style={[
                        styles.progressFill,
                        { width: `${getTimerProgress()}%` }
                      ]}
                    />
                  </View>
                  <Text style={styles.progressText}>
                    {Math.round(getTimerProgress())}% Complete
                  </Text>
                </View>
              )}
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Timer Controls */}
        <View style={styles.timerControls}>
          <TouchableOpacity style={styles.controlButton} onPress={resetTimer}>
            <RotateCcw size={24} color="#6366F1" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.playButton} onPress={isRunning ? pauseTimer : startTimer}>
            <LinearGradient
              colors={['#6366F1', '#8B5CF6']}
              style={styles.playGradient}
            >
              {isRunning ? (
                <Pause size={32} color="#FFFFFF" />
              ) : (
                <Play size={32} color="#FFFFFF" />
              )}
            </LinearGradient>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.controlButton} onPress={stopTimer}>
            <Square size={24} color="#EF4444" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Today's Stats */}
      <View style={styles.statsSection}>
        <Text style={styles.sectionTitle}>Today's Progress</Text>
        
        <View style={styles.statsCard}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{formatTime(getTotalTimeToday())}</Text>
            <Text style={styles.statLabel}>Total Time</Text>
          </View>
          
          <View style={styles.statDivider} />
          
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {currentSubject ? currentSubject.name : 'No Subject'}
            </Text>
            <Text style={styles.statLabel}>Current Subject</Text>
          </View>
        </View>
      </View>

      {/* Quick Tips */}
      <View style={styles.tipsSection}>
        <Text style={styles.sectionTitle}>Tips</Text>
        
        <View style={styles.tipCard}>
          <Text style={styles.tipTitle}>
            {mode === 'stopwatch' ? '⏱️ Stopwatch Mode' : '🍅 Pomodoro Technique'}
          </Text>
          <Text style={styles.tipText}>
            {mode === 'stopwatch' 
              ? 'Perfect for open-ended study sessions. Track your time without pressure.'
              : 'Work for 25 minutes, then take a 5-minute break. After 4 sessions, take a longer break.'
            }
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#030303',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'transparent',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  settingsButton: {
    width: 40,
    height: 40,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  modeSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  modeSelector: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 20,
    padding: 6,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.12)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  modeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 18,
    borderRadius: 16,
    gap: 8,
  },
  modeButtonActive: {
    backgroundColor: 'rgba(99,102,241,0.8)',
  },
  modeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255,255,255,0.6)',
  },
  modeTextActive: {
    color: '#FFFFFF',
  },
  subjectSection: {
    paddingHorizontal: 24,
  },
  timerSection: {
    alignItems: 'center',
    marginVertical: 32,
    paddingHorizontal: 20,
  },
  timerContainer: {
    marginBottom: 32,
    width: '100%',
  },
  timerCard: {
    borderRadius: 32,
    padding: 32,
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.12)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.15,
    shadowRadius: 40,
    elevation: 20,
  },
  timerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    gap: 16,
  },
  phaseIndicator: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  phaseGradient: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  phaseIcon: {
    fontSize: 24,
  },
  phaseText: {
    fontSize: 18,
    fontFamily: 'Inter-Semibold',
    color: '#FFFFFF',
    flex: 1,
  },
  timerDisplay: {
    alignItems: 'center',
  },
  timerText: {
    fontSize: 64,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 24,
    letterSpacing: -2,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    gap: 12,
  },
  progressTrack: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255,255,255,0.7)',
  },
  timerControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20,
    marginTop: 8,
  },
  controlButton: {
    width: 60,
    height: 60,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  playButton: {
    width: 80,
    height: 80,
    borderRadius: 24,
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 12,
  },
  playGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  statsCard: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 20,
    padding: 24,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.12)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255,255,255,0.7)',
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(255,255,255,0.15)',
    marginHorizontal: 20,
  },
  tipsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
    marginBottom: 32,
  },
  tipCard: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 20,
    padding: 24,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.12)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255,255,255,0.7)',
    lineHeight: 20,
  },
});